# 第5章 系统设计

## 5.1 系统架构设计

### 5.1.1 系统架构概述

本仓库管理系统采用B/S（Browser/Server）架构模式，基于Web技术栈实现前后端分离的现代化架构设计。系统采用三层架构模式，包括表现层、业务逻辑层和数据访问层，确保系统的可扩展性、可维护性和高性能。

### 5.1.2 系统架构图

如上图所示，本仓库管理系统采用现代化的B/S架构设计，具有清晰的层次结构和模块化设计。系统架构从上到下分为六个层次：客户端层、表现层、业务逻辑层、API接口层、数据访问层和数据存储层。客户端层支持多种设备访问，包括桌面浏览器、移动端浏览器等，确保系统的跨平台兼容性。表现层采用HTML5、CSS3和JavaScript ES6+技术，结合Bootstrap 5框架实现响应式设计，为用户提供现代化的交互体验。业务逻辑层包含14个核心功能模块，涵盖用户管理、物品管理、库存管理、申请管理、报表管理等完整的仓库管理功能。API接口层基于RESTful架构设计，使用Express.js框架和中间件技术，确保前后端的高效通信。数据访问层和数据存储层采用内存数据库结合文件系统的混合存储方案，既保证了数据访问的高性能，又确保了数据的持久化存储和备份恢复能力。整个架构设计遵循松耦合、高内聚的原则，具有良好的可扩展性和可维护性。

## 5.2 系统整体功能设计

### 5.2.1 系统功能模块图

如上图所示，本仓库管理系统包含14个主要功能模块，每个模块都具有完整的子功能。系统功能模块设计遵循模块化、层次化的原则，确保功能的完整性和可扩展性。用户认证模块负责系统的安全访问控制，包括用户登录、注册、登出和角色权限验证等功能。用户管理模块提供完整的用户生命周期管理，支持用户的增删改查和角色分配。物品管理模块是系统的核心模块之一，提供物品信息的全面管理功能。库存管理模块实现库存的实时监控和调整，确保库存数据的准确性。申请管理模块支持完整的申请流程，从提交到审批再到跟踪。报表管理模块提供多维度的数据分析和报表生成功能。日志管理模块记录系统的所有操作和事件，确保系统的可追溯性。数据备份模块保障数据的安全性和可恢复性。供应商管理、分类管理、仓库管理等模块提供基础数据的维护功能。预警管理模块实现智能预警和通知功能。统计分析模块提供数据可视化和决策支持。系统配置模块允许管理员自定义系统参数和界面设置。

### 5.2.2 功能模块说明

系统共包含14个核心功能模块，超过60个子功能，完全满足现代仓库管理的需求。每个模块都具有独立的业务逻辑和数据处理能力，模块间通过标准化的接口进行通信，确保系统的松耦合设计。

## 5.3 系统关键功能详细设计

### 5.3.1 库存管理流程设计

如上图所示，库存管理流程是系统的核心业务流程之一。流程从用户身份验证开始，确保只有授权用户才能访问库存管理功能。验证成功后，用户进入库存管理界面，可以选择查看库存、添加物品、调整库存或搜索物品等操作。查看库存功能会自动检查预警状态，当库存低于设定阈值时，系统会显示预警信息并提供补货申请选项。添加物品功能包含完整的输入验证机制，确保数据的准确性和完整性。调整库存功能支持库存数量的增减操作，并在调整后自动检查库存阈值，触发相应的预警机制。搜索物品功能提供灵活的查询方式，支持关键词搜索和结果筛选。整个流程设计注重用户体验和数据安全，每个关键操作都会记录操作日志，确保系统的可追溯性和审计能力。流程中的预警机制能够及时发现库存异常，帮助管理员做出正确的决策。

### 5.3.2 申请审批流程设计

如上图所示，申请审批流程是系统的另一个核心业务流程。流程支持入库申请和出库申请两种类型，每种类型都有相应的验证机制。用户首先需要通过身份验证，然后选择申请类型并填写相应的申请表单。系统会对申请信息进行严格的验证，包括数据格式验证和业务逻辑验证。对于出库申请，系统还会检查库存是否充足，确保申请的可执行性。申请提交后，系统会自动通知业务管理员进行审核。管理员可以选择批准、拒绝或请求补充信息。批准的申请会自动更新库存并记录交易信息，拒绝的申请会记录拒绝原因。如果需要补充信息，系统会通知用户并允许其更新申请内容。整个流程设计确保了申请处理的规范性和透明性，所有操作都有完整的记录和通知机制，提高了工作效率和用户满意度。

### 5.3.3 用户权限管理流程设计

如上图所示，用户权限管理流程确保了系统的安全性和权限控制的规范性。流程从系统管理员登录开始，只有具备系统管理员权限的用户才能进行用户管理操作。系统提供了完整的用户生命周期管理功能，包括添加、编辑、删除用户和角色分配。添加用户时，系统会进行严格的信息验证和用户名重复检查，确保用户信息的唯一性和完整性。编辑用户功能允许管理员修改用户信息，但需要通过验证确保修改的合法性。删除用户功能包含安全检查，防止误删系统管理员账号。角色分配功能支持三种角色：系统管理员、业务管理员和普通用户，每种角色都有相应的权限验证机制。用户列表查看功能提供多种筛选方式，便于管理员快速定位目标用户。所有用户管理操作都会记录详细的操作日志，确保系统的可审计性和安全性。

### 5.3.4 数据备份恢复流程设计

数据备份恢复流程是系统数据安全的重要保障。系统支持手动备份和自动备份两种方式，备份内容包括用户数据、物品信息、库存记录、申请历史和系统配置等。备份文件采用JSON格式存储，便于数据的导入导出和跨平台兼容。恢复功能支持从备份文件中选择性恢复数据，确保数据的完整性和一致性。

## 5.4 数据库设计

### 5.4.1 数据库概念设计

如上图所示，本仓库管理系统的数据库概念设计采用实体关系模型，包含10个核心实体和多种关系类型。数据库设计遵循第三范式，确保数据的一致性和完整性。用户实体（USERS）是系统的核心实体，与其他多个实体建立关联关系，支持用户的各种操作记录。物品实体（INVENTORY_ITEMS）是业务的核心，与分类（CATEGORIES）、供应商（SUPPLIERS）、仓库（WAREHOUSES）建立多对一关系，形成完整的物品信息体系。申请实体（REQUESTS）连接用户和物品，记录完整的申请流程信息。库存操作实体（INVENTORY_OPERATIONS）记录所有库存变动，与用户、物品和申请建立关联。系统日志实体（SYSTEM_LOGS）记录用户的所有操作行为，确保系统的可追溯性。系统配置实体（SYSTEM_CONFIG）存储系统参数，支持系统的灵活配置。备份实体（BACKUPS）管理数据备份信息，保障数据安全。整个E-R图设计体现了仓库管理业务的完整性和数据关系的合理性，为系统的高效运行提供了坚实的数据基础。

### 5.4.2 数据库逻辑设计

数据库逻辑设计将概念模型转换为具体的数据库表结构，每个实体对应一个数据表，关系通过外键约束实现。主要数据表包括：

**用户表（users）**：存储用户基本信息，包括用户名、密码、角色、真实姓名、邮箱等字段，支持三种角色的权限管理。

**物品表（inventory_items）**：存储物品详细信息，包括名称、描述、数量、价格、库存阈值等，通过外键关联分类、供应商和仓库。

**申请表（requests）**：记录用户的入库和出库申请，包括申请类型、数量、用途、状态、审批信息等，支持完整的申请流程管理。

**库存操作表（inventory_operations）**：记录所有库存变动操作，包括操作类型、数量、价格、操作时间等，确保库存变动的可追溯性。

**系统日志表（system_logs）**：记录用户操作日志，包括操作类型、详细信息、IP地址、时间戳等，支持系统审计和安全监控。

数据表之间通过外键约束建立关联关系，确保数据的参照完整性。同时设计了适当的索引策略，提高查询性能。

## 5.5 关于开发环境

### 5.5.1 技术栈选择

本仓库管理系统采用现代化的Web技术栈，具体包括：

**前端技术栈**：
- HTML5：提供语义化的页面结构
- CSS3：实现现代化的样式设计和动画效果
- JavaScript ES6+：提供丰富的交互功能和异步处理
- Bootstrap 5：响应式UI框架，确保跨设备兼容性

**后端技术栈**：
- Node.js：高性能的JavaScript运行环境
- Express.js：轻量级的Web应用框架
- RESTful API：标准化的接口设计
- CORS：跨域资源共享支持

**数据存储**：
- 内存数据库：高性能的数据访问
- JSON文件：数据持久化存储
- 文件系统：备份和日志存储

### 5.5.2 开发工具和环境

**开发工具**：
- Visual Studio Code：主要开发IDE
- Node.js v16+：JavaScript运行环境
- npm：包管理工具
- Git：版本控制系统

**部署环境**：
- 操作系统：Windows/Linux/macOS
- Web服务器：Express.js内置服务器
- 端口配置：默认3000端口
- 浏览器支持：Chrome、Firefox、Safari、Edge等现代浏览器

**开发规范**：
- 代码风格：遵循ES6+标准
- 模块化设计：前后端分离架构
- API设计：RESTful风格
- 错误处理：统一的错误处理机制
- 日志记录：完整的操作日志系统

系统采用前后端分离的开发模式，前端负责用户界面和交互逻辑，后端提供API服务和数据处理，通过HTTP协议进行通信。这种架构设计提高了系统的可维护性和可扩展性，便于团队协作开发和后期维护升级。